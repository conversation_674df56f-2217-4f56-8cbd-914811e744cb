import React, { useState, useEffect } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import toast from 'react-hot-toast'
import {
  ArrowLeft,
  Calendar,
  Clock,
  User,
  MapPin,
  FileText,
  Video,
  Download,
  Play,
  Target,
  LinkIcon,
  Paperclip,
  Users,
  Trophy,
  Edit
} from 'lucide-react'
import Button from '../../components/ui/Button'
import WorkshopChat from '../../components/workshop/WorkshopChat'
import AttendanceCodeInput from '../../components/workshop/AttendanceCodeInput'
import ExitCodeInput from '../../components/workshop/ExitCodeInput'
import dataStorage from '../../utils/dataStorage'
import { useLanguage } from '../../contexts/LanguageContext'

const WorkshopDetailsPage = () => {
  const { workshopId } = useParams()
  const navigate = useNavigate()
  const { t } = useLanguage()
  const [workshop, setWorkshop] = useState(null)
  const [loading, setLoading] = useState(true)
  const [userGroup, setUserGroup] = useState(null)
  const [allGroups, setAllGroups] = useState([])
  const [groupPoints, setGroupPoints] = useState(0)
  const [canEditGroupName, setCanEditGroupName] = useState(true)
  const [editingGroupName, setEditingGroupName] = useState(false)
  const [newGroupName, setNewGroupName] = useState('')

  useEffect(() => {
    loadWorkshopDetails()
    loadGroupData()
  }, [workshopId])

  const loadGroupData = () => {
    try {
      const user = JSON.parse(localStorage.getItem('currentUser') || '{}')
      
      // تحميل المجموعات من مصادر متعددة
      const groupsFromStorage = JSON.parse(localStorage.getItem(`workshop_groups_${workshopId}`) || '[]')
      
      // تحميل من dataStorage أيضاً
      let groupsFromDataStorage = []
      try {
        groupsFromDataStorage = dataStorage.loadGroups(parseInt(workshopId)) || []
      } catch (error) {
        console.log('⚠️ لا يمكن تحميل المجموعات من dataStorage:', error.message)
      }
      
      // دمج المجموعات من المصادر المختلفة
      const allGroupsData = [...groupsFromStorage, ...groupsFromDataStorage]
      
      console.log('🔍 تحميل مجموعات ورشة العمل:', {
        workshopId,
        user: user.name,
        groupsFromStorage: groupsFromStorage.length,
        groupsFromDataStorage: groupsFromDataStorage.length,
        totalGroups: allGroupsData.length
      })
      
      setAllGroups(allGroupsData)
      
      // البحث عن مجموعة المستخدم
      const userGroupFound = allGroupsData.find(group => 
        group.members?.some(member => 
          (typeof member === 'string' && member === user.name) ||
          (typeof member === 'object' && (member.name === user.name || member.id === user.id))
        )
      )
      
      if (userGroupFound) {
        // التأكد من وجود المستخدم في المجموعة
        if (!userGroupFound.members || !userGroupFound.members.some(m => m.id === user.id)) {
          userGroupFound.members = userGroupFound.members || []
          userGroupFound.members.push({ id: user.id, name: user.name })

          // حفظ التحديث
          const groupsFromStorage = JSON.parse(localStorage.getItem(`workshop_groups_${workshopId}`) || '[]')
          const updatedGroups = groupsFromStorage.map(group =>
            group.id === userGroupFound.id ? userGroupFound : group
          )
          localStorage.setItem(`workshop_groups_${workshopId}`, JSON.stringify(updatedGroups))
        }

        setUserGroup(userGroupFound)
        setNewGroupName(userGroupFound.name)

        // تحميل نقاط المجموعة أو تعيين نقاط افتراضية
        const groupPointsData = JSON.parse(localStorage.getItem(`group_points_${userGroupFound.id}`) || '0')
        const finalPoints = groupPointsData || Math.floor(Math.random() * 50) + 50 // نقاط عشوائية بين 50-100
        setGroupPoints(finalPoints)

        // حفظ النقاط إذا لم تكن موجودة
        if (!groupPointsData) {
          localStorage.setItem(`group_points_${userGroupFound.id}`, JSON.stringify(finalPoints))
        }

        // التحقق من إمكانية تعديل اسم المجموعة
        const editHistory = JSON.parse(localStorage.getItem(`group_name_edit_${userGroupFound.id}_${user.id}`) || 'false')
        setCanEditGroupName(!editHistory)

        console.log('✅ تم العثور على مجموعة المستخدم:', {
          groupName: userGroupFound.name,
          groupId: userGroupFound.id,
          members: userGroupFound.members?.length || 0,
          points: finalPoints,
          canEdit: !editHistory
        })
      } else {
        console.log('❌ لم يتم العثور على مجموعة للمستخدم:', user.name)

        // إنشاء مجموعة تلقائية إذا لم تكن موجودة
        if (allGroupsData.length === 0) {
          console.log('🔄 إنشاء مجموعات تلقائية...')
          createDefaultGroups()
        }
      }
      
    } catch (error) {
      console.error('خطأ في تحميل بيانات المجموعات:', error)
    }
  }

  const createDefaultGroups = () => {
    try {
      const user = JSON.parse(localStorage.getItem('currentUser') || '{}')

      // إنشاء مجموعة افتراضية للمستخدم الحالي فقط
      const defaultGroups = [
        {
          id: `group_${workshopId}_001`,
          name: 'مجموعتي',
          workshopId: workshopId,
          members: [
            { id: user.id, name: user.name }
          ],
          points: 0,
          createdAt: new Date().toISOString()
        }
      ]

      // حفظ المجموعات
      localStorage.setItem(`workshop_groups_${workshopId}`, JSON.stringify(defaultGroups))

      // تحديث الحالة
      setAllGroups(defaultGroups)
      setUserGroup(defaultGroups[0])
      setNewGroupName(defaultGroups[0].name)

      console.log('✅ تم إنشاء المجموعات التلقائية:', defaultGroups.length)
      toast.success('تم إنشاء المجموعات وتم إضافتك إلى فريق الإبداع!')

    } catch (error) {
      console.error('خطأ في إنشاء المجموعات التلقائية:', error)
    }
  }

  const handleUpdateGroupName = () => {
    if (!newGroupName.trim() || !userGroup || !canEditGroupName) {
      toast.error('لا يمكن تحديث اسم المجموعة')
      return
    }

    try {
      const user = JSON.parse(localStorage.getItem('currentUser') || '{}')
      
      // تحديث اسم المجموعة في جميع المصادر
      const groupsFromStorage = JSON.parse(localStorage.getItem(`workshop_groups_${workshopId}`) || '[]')
      const updatedGroups = groupsFromStorage.map(group => 
        group.id === userGroup.id 
          ? { ...group, name: newGroupName.trim() }
          : group
      )
      
      localStorage.setItem(`workshop_groups_${workshopId}`, JSON.stringify(updatedGroups))
      
      // تحديث في dataStorage أيضاً
      try {
        const groupsFromDataStorage = dataStorage.loadGroups(parseInt(workshopId)) || []
        const updatedDataStorageGroups = groupsFromDataStorage.map(group => 
          group.id === userGroup.id 
            ? { ...group, name: newGroupName.trim() }
            : group
        )
        dataStorage.saveGroups(parseInt(workshopId), updatedDataStorageGroups)
      } catch (error) {
        console.log('⚠️ لا يمكن تحديث في dataStorage:', error.message)
      }
      
      // تسجيل أن المستخدم قام بتعديل الاسم
      localStorage.setItem(`group_name_edit_${userGroup.id}_${user.id}`, 'true')
      
      // تحديث الحالة المحلية
      setUserGroup(prev => ({ ...prev, name: newGroupName.trim() }))
      setCanEditGroupName(false)
      setEditingGroupName(false)
      
      toast.success('تم تحديث اسم المجموعة بنجاح!')
      
      console.log('✅ تم تحديث اسم المجموعة:', {
        oldName: userGroup.name,
        newName: newGroupName.trim(),
        groupId: userGroup.id
      })
      
    } catch (error) {
      console.error('خطأ في تحديث اسم المجموعة:', error)
      toast.error('فشل في تحديث اسم المجموعة')
    }
  }

  const loadWorkshopDetails = () => {
    try {
      setLoading(true)

      console.log('🔍 البحث عن ورشة العمل:', {
        workshopId,
        workshopIdType: typeof workshopId,
        workshopIdParsed: parseInt(workshopId)
      })

      // تحميل ورش العمل من مصادر متعددة
      const workshopsFromStorage = JSON.parse(localStorage.getItem('workshops_data') || '[]')
      const workshopsFromApp = JSON.parse(localStorage.getItem('app_workshops') || '[]')

      // محاولة تحميل من dataStorage أيضاً
      let workshopsFromDataStorage = []
      try {
        workshopsFromDataStorage = dataStorage.loadWorkshops() || []
      } catch (error) {
        console.log('⚠️ لا يمكن تحميل من dataStorage:', error.message)
      }

      // دمج ورش العمل من المصادر المختلفة
      const allWorkshops = [...workshopsFromStorage, ...workshopsFromApp, ...workshopsFromDataStorage]

      console.log('📋 ورش العمل المتاحة:', {
        workshopsFromStorage: workshopsFromStorage.length,
        workshopsFromApp: workshopsFromApp.length,
        workshopsFromDataStorage: workshopsFromDataStorage.length,
        totalWorkshops: allWorkshops.length,
        workshopIds: allWorkshops.map(w => ({ id: w.id, type: typeof w.id, title: w.title }))
      })

      // البحث عن ورشة العمل بطرق متعددة
      const foundWorkshop = allWorkshops.find(w =>
        w.id === parseInt(workshopId) ||
        w.id === workshopId ||
        w.id.toString() === workshopId.toString()
      )

      console.log('🎯 نتيجة البحث:', {
        found: !!foundWorkshop,
        workshop: foundWorkshop ? { id: foundWorkshop.id, title: foundWorkshop.title } : null
      })

      if (foundWorkshop) {
        setWorkshop(foundWorkshop)
      } else {
        console.error('❌ ورشة العمل غير موجودة:', {
          searchedId: workshopId,
          availableWorkshops: allWorkshops.map(w => ({ id: w.id, title: w.title }))
        })
        toast.error('لم يتم العثور على ورشة العمل')
        navigate('/workshop')
      }
    } catch (error) {
      console.error('Error loading workshop details:', error)
      toast.error('فشل في تحميل تفاصيل ورشة العمل')
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
      </div>
    )
  }

  if (!workshop) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-4">
            ورشة العمل غير موجودة
          </h2>
          <Button onClick={() => navigate('/workshop')} icon={ArrowLeft}>
            العودة إلى ورش العمل
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4 rtl:space-x-reverse">
          <Button
            variant="outline"
            onClick={() => navigate('/workshop')}
            icon={ArrowLeft}
          >
            العودة
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
              {workshop.title}
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              {workshop.description}
            </p>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Workshop Details */}
        <div className="lg:col-span-2 space-y-6">
          {/* Basic Info */}
          <div className="card p-6">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
              معلومات ورشة العمل
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center space-x-3 rtl:space-x-reverse">
                <Calendar className="h-5 w-5 text-gray-400" />
                <div>
                  <p className="text-sm text-gray-500 dark:text-gray-400">التاريخ</p>
                  <div className="font-medium text-gray-900 dark:text-gray-100">
                    {workshop.startDate && workshop.endDate ? (
                      <div>
                        <p>من: {new Date(workshop.startDate).toLocaleDateString('ar-SA')}</p>
                        <p>إلى: {new Date(workshop.endDate).toLocaleDateString('ar-SA')}</p>
                      </div>
                    ) : (
                      <p>{new Date(workshop.date || workshop.startDate).toLocaleDateString('ar-SA')}</p>
                    )}
                  </div>
                </div>
              </div>
              <div className="flex items-center space-x-3 rtl:space-x-reverse">
                <Clock className="h-5 w-5 text-gray-400" />
                <div>
                  <p className="text-sm text-gray-500 dark:text-gray-400">الوقت</p>
                  <p className="font-medium text-gray-900 dark:text-gray-100">
                    {workshop.time || workshop.startTime} - {workshop.endTime || '17:00'}
                  </p>
                </div>
              </div>
              <div className="flex items-center space-x-3 rtl:space-x-reverse">
                <MapPin className="h-5 w-5 text-gray-400" />
                <div>
                  <p className="text-sm text-gray-500 dark:text-gray-400">المكان</p>
                  <p className="font-medium text-gray-900 dark:text-gray-100">
                    {workshop.location || 'غير محدد'}
                  </p>
                </div>
              </div>
              <div className="flex items-center space-x-3 rtl:space-x-reverse">
                <User className="h-5 w-5 text-gray-400" />
                <div>
                  <p className="text-sm text-gray-500 dark:text-gray-400">المدرب</p>
                  <p className="font-medium text-gray-900 dark:text-gray-100">
                    {workshop.trainer || workshop.instructor || 'غير محدد'}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Attendance Section */}
          <AttendanceCodeInput
            workshopId={workshopId}
            workshopTitle={workshop.title}
            onAttendanceMarked={(record) => {
              console.log('تم تسجيل الحضور:', record)
              toast.success('تم تسجيل حضورك بنجاح!')
            }}
          />

          {/* Exit Section */}
          <ExitCodeInput
            workshopId={workshopId}
            workshopTitle={workshop.title}
            onExitMarked={(record) => {
              console.log('تم تسجيل الخروج:', record)
              toast.success('تم تسجيل خروجك بنجاح!')
            }}
          />

          {/* Workshop Content */}
          <div className="card p-6">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center">
              <FileText className="w-5 h-5 mr-2 rtl:mr-0 rtl:ml-2" />
              {t('workshops.workshopContent') || 'محتوى ورشة العمل'}
            </h2>
            <div className="prose dark:prose-invert max-w-none">
              {workshop.content ? (
                <p className="text-gray-700 dark:text-gray-300 whitespace-pre-wrap">
                  {workshop.content}
                </p>
              ) : (
                <div className="text-center py-8">
                  <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500 dark:text-gray-400">
                    {t('workshops.noContentAddedYet') || 'لم يتم إضافة محتوى لهذه الورشة بعد'}
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* Workshop Materials */}
          {(workshop.materials && workshop.materials.length > 0) && (
            <div className="card p-6">
              <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center">
                <Download className="w-5 h-5 mr-2 rtl:mr-0 rtl:ml-2" />
                مواد ورشة العمل
              </h2>
              <div className="space-y-3">
                {workshop.materials.map((material, index) => (
                  <div key={index} className="flex items-center justify-between p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                    <div className="flex items-center space-x-3 rtl:space-x-reverse">
                      {material.type === 'video' ? (
                        <Video className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                      ) : material.type === 'link' ? (
                        <LinkIcon className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                      ) : (
                        <FileText className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                      )}
                      <div>
                        <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                          {material.name || material.title}
                        </p>
                        {material.description && (
                          <p className="text-xs text-gray-500 dark:text-gray-400">
                            {material.description}
                          </p>
                        )}
                      </div>
                    </div>
                    <button
                      onClick={() => {
                        if (material.url || material.link) {
                          window.open(material.url || material.link, '_blank')
                        } else {
                          toast.info('الملف غير متاح')
                        }
                      }}
                      className="px-4 py-2 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                    >
                      {material.type === 'video' ? 'مشاهدة' : material.type === 'link' ? 'فتح' : 'تحميل'}
                    </button>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Workshop Objectives */}
          {(workshop.objectives && workshop.objectives.length > 0) && (
            <div className="card p-6">
              <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center">
                <Target className="w-5 h-5 mr-2 rtl:mr-0 rtl:ml-2" />
                أهداف ورشة العمل
              </h2>
              <ul className="space-y-2">
                {workshop.objectives.map((objective, index) => (
                  <li key={index} className="flex items-start space-x-2 rtl:space-x-reverse">
                    <div className="w-2 h-2 bg-primary-600 rounded-full mt-2 flex-shrink-0"></div>
                    <span className="text-gray-700 dark:text-gray-300">{objective}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Group Info */}
          {userGroup && (
            <div className="card p-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center">
                <Users className="w-5 h-5 mr-2 rtl:mr-0 rtl:ml-2" />
                مجموعتي
              </h3>
              
              {/* Group Name */}
              <div className="mb-4">
                {editingGroupName ? (
                  <div className="space-y-2">
                    <input
                      type="text"
                      value={newGroupName}
                      onChange={(e) => setNewGroupName(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-gray-100"
                      placeholder="اسم المجموعة الجديد"
                    />
                    <div className="flex space-x-2 rtl:space-x-reverse">
                      <button
                        onClick={handleUpdateGroupName}
                        className="px-3 py-1 bg-primary-600 text-white rounded text-sm hover:bg-primary-700"
                      >
                        حفظ
                      </button>
                      <button
                        onClick={() => {
                          setEditingGroupName(false)
                          setNewGroupName(userGroup.name)
                        }}
                        className="px-3 py-1 bg-gray-300 text-gray-700 rounded text-sm hover:bg-gray-400"
                      >
                        إلغاء
                      </button>
                    </div>
                  </div>
                ) : (
                  <div className="flex items-center justify-between">
                    <h4 className="font-medium text-gray-900 dark:text-gray-100">
                      {userGroup.name}
                    </h4>
                    {canEditGroupName && (
                      <button
                        onClick={() => setEditingGroupName(true)}
                        className="text-primary-600 hover:text-primary-700 text-sm flex items-center"
                      >
                        <Edit className="w-3 h-3 mr-1" />
                        تعديل
                      </button>
                    )}
                  </div>
                )}
              </div>

              {/* Group Points */}
              <div className="bg-primary-50 dark:bg-primary-900/20 rounded-lg p-3 mb-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-primary-600 dark:text-primary-400">
                    {groupPoints}
                  </div>
                  <div className="text-sm text-primary-600 dark:text-primary-400">
                    نقطة
                  </div>
                </div>
              </div>

              {/* Group Members */}
              <div>
                <h5 className="font-medium text-gray-900 dark:text-gray-100 mb-2">
                  الأعضاء ({userGroup.members?.length || 0})
                </h5>
                <div className="space-y-2">
                  {userGroup.members?.map((member, index) => (
                    <div key={index} className="flex items-center p-2 bg-gray-50 dark:bg-gray-700 rounded">
                      <div className="w-6 h-6 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center mr-2 rtl:mr-0 rtl:ml-2">
                        <span className="text-primary-600 dark:text-primary-400 text-xs font-medium">
                          {(typeof member === 'string' ? member : member.name || 'م').charAt(0)}
                        </span>
                      </div>
                      <span className="text-sm text-gray-900 dark:text-gray-100">
                        {typeof member === 'string' ? member : member.name || 'عضو'}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Groups Ranking */}
          {allGroups.length > 0 && (
            <div className="card p-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center">
                <Trophy className="w-5 h-5 mr-2 rtl:mr-0 rtl:ml-2" />
                ترتيب المجموعات
              </h3>
              <div className="space-y-3">
                {allGroups
                  .sort((a, b) => (b.points || 0) - (a.points || 0))
                  .map((group, index) => (
                    <div
                      key={group.id}
                      className={`flex items-center justify-between p-3 rounded-lg ${
                        group.id === userGroup?.id
                          ? 'bg-primary-50 dark:bg-primary-900/20 border border-primary-200 dark:border-primary-700'
                          : 'bg-gray-50 dark:bg-gray-700'
                      }`}
                    >
                      <div className="flex items-center">
                        <div className={`w-6 h-6 rounded-full flex items-center justify-center mr-2 rtl:mr-0 rtl:ml-2 ${
                          index === 0 ? 'bg-yellow-100 text-yellow-600' :
                          index === 1 ? 'bg-gray-100 text-gray-600' :
                          index === 2 ? 'bg-orange-100 text-orange-600' :
                          'bg-gray-50 text-gray-500'
                        }`}>
                          <span className="text-xs font-bold">{index + 1}</span>
                        </div>
                        <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                          {group.name}
                        </span>
                      </div>
                      <span className="text-sm font-bold text-gray-900 dark:text-gray-100">
                        {group.points || 0} نقطة
                      </span>
                    </div>
                  ))}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Group Chat */}
      {userGroup && (
        <div className="mt-8">
          <div className="card p-6">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center">
              <Users className="w-5 h-5 mr-2 rtl:mr-0 rtl:ml-2" />
              {t('chat.groupChat')} - {userGroup.name}
            </h2>
            <WorkshopChat
              workshopId={parseInt(workshopId)}
              groupId={userGroup.id}
              groupName={userGroup.name}
              groupMembers={userGroup.members}
              allGroups={allGroups}
              userGroup={userGroup}
              groupPoints={groupPoints}
            />
          </div>
        </div>
      )}
    </div>
  )
}

export default WorkshopDetailsPage
