// خدمة إدارة الحضور والإشعارات
class AttendanceService {
  constructor() {
    this.notificationService = null
  }

  // تهيئة خدمة الإشعارات
  setNotificationService(notificationService) {
    this.notificationService = notificationService
  }

  // تسجيل حضور بالكود
  async markAttendanceByCode(workshopId, code, user) {
    try {
      // التحقق من صحة الكود
      const savedCode = localStorage.getItem(`workshop_code_${workshopId}`)
      const savedExpiry = localStorage.getItem(`workshop_code_expiry_${workshopId}`)

      if (!savedCode || !savedExpiry) {
        throw new Error('لا يوجد كود حضور نشط لهذه الورشة')
      }

      const expiryTime = new Date(savedExpiry)
      if (expiryTime <= new Date()) {
        throw new Error('انتهت صلاحية كود الحضور')
      }

      if (code.toUpperCase() !== savedCode.toUpperCase()) {
        throw new Error('كود الحضور غير صحيح')
      }

      // تسجيل الحضور
      const attendanceRecords = JSON.parse(localStorage.getItem(`workshop_attendance_${workshopId}`) || '[]')
      
      const existingRecord = attendanceRecords.find(record => 
        record.participantName === user.name || record.participantId === user.id
      )

      const newRecord = {
        participantName: user.name,
        participantId: user.id,
        status: 'present',
        timestamp: new Date().toISOString(),
        method: 'code',
        code: code.toUpperCase()
      }

      let updatedRecords
      if (existingRecord) {
        updatedRecords = attendanceRecords.map(record =>
          (record.participantName === user.name || record.participantId === user.id) 
            ? newRecord 
            : record
        )
      } else {
        updatedRecords = [...attendanceRecords, newRecord]
      }

      localStorage.setItem(`workshop_attendance_${workshopId}`, JSON.stringify(updatedRecords))

      // إرسال إشعار للمدرب
      await this.notifyTrainerOfAttendance(workshopId, user, 'code')

      return newRecord
    } catch (error) {
      console.error('Error marking attendance by code:', error)
      throw error
    }
  }

  // تسجيل خروج بالكود
  async markExitByCode(workshopId, code, user) {
    try {
      // التحقق من صحة كود الخروج
      const savedExitCode = localStorage.getItem(`workshop_exit_code_${workshopId}`)
      const savedExitExpiry = localStorage.getItem(`workshop_exit_code_expiry_${workshopId}`)

      if (!savedExitCode || !savedExitExpiry) {
        throw new Error('لا يوجد كود خروج نشط لهذه الورشة')
      }

      const expiryTime = new Date(savedExitExpiry)
      if (expiryTime <= new Date()) {
        throw new Error('انتهت صلاحية كود الخروج')
      }

      if (code.toUpperCase() !== savedExitCode.toUpperCase()) {
        throw new Error('كود الخروج غير صحيح')
      }

      // تسجيل الخروج
      const attendanceRecords = JSON.parse(localStorage.getItem(`workshop_attendance_${workshopId}`) || '[]')

      const existingRecord = attendanceRecords.find(record =>
        record.participantName === user.name || record.participantId === user.id
      )

      const exitRecord = {
        participantName: user.name,
        participantId: user.id,
        status: 'exit',
        timestamp: new Date().toISOString(),
        method: 'code',
        code: code.toUpperCase(),
        exitTime: new Date().toISOString()
      }

      let updatedRecords
      if (existingRecord) {
        // تحديث السجل الموجود بإضافة وقت الخروج
        updatedRecords = attendanceRecords.map(record =>
          (record.participantName === user.name || record.participantId === user.id)
            ? { ...record, exitTime: new Date().toISOString(), exitMethod: 'code', exitCode: code.toUpperCase() }
            : record
        )
      } else {
        // إنشاء سجل جديد للخروج فقط
        updatedRecords = [...attendanceRecords, exitRecord]
      }

      localStorage.setItem(`workshop_attendance_${workshopId}`, JSON.stringify(updatedRecords))

      // إرسال إشعار للمدرب
      await this.notifyTrainerOfExit(workshopId, user, 'code')

      return exitRecord
    } catch (error) {
      console.error('Error marking exit by code:', error)
      throw error
    }
  }

  // تسجيل حضور يدوي
  async markAttendanceManually(workshopId, participantName, status, markedBy) {
    try {
      const attendanceRecords = JSON.parse(localStorage.getItem(`workshop_attendance_${workshopId}`) || '[]')
      
      const existingRecord = attendanceRecords.find(record => record.participantName === participantName)
      const newRecord = {
        participantName,
        status,
        timestamp: new Date().toISOString(),
        method: 'manual',
        markedBy
      }

      let updatedRecords
      if (existingRecord) {
        updatedRecords = attendanceRecords.map(record =>
          record.participantName === participantName ? newRecord : record
        )
      } else {
        updatedRecords = [...attendanceRecords, newRecord]
      }

      localStorage.setItem(`workshop_attendance_${workshopId}`, JSON.stringify(updatedRecords))

      // إرسال إشعار للمتدرب
      await this.notifyTraineeOfAttendance(participantName, status, workshopId)

      return newRecord
    } catch (error) {
      console.error('Error marking attendance manually:', error)
      throw error
    }
  }

  // تسجيل خروج يدوي
  async markExitManually(workshopId, participantName, markedBy) {
    try {
      const attendanceRecords = JSON.parse(localStorage.getItem(`workshop_attendance_${workshopId}`) || '[]')

      const existingRecord = attendanceRecords.find(record => record.participantName === participantName)

      if (existingRecord) {
        // تحديث السجل الموجود بإضافة وقت الخروج
        const updatedRecords = attendanceRecords.map(record =>
          record.participantName === participantName
            ? {
                ...record,
                exitTime: new Date().toISOString(),
                exitMethod: 'manual',
                exitMarkedBy: markedBy
              }
            : record
        )
        localStorage.setItem(`workshop_attendance_${workshopId}`, JSON.stringify(updatedRecords))
      } else {
        // إنشاء سجل جديد للخروج فقط
        const exitRecord = {
          participantName,
          status: 'exit',
          timestamp: new Date().toISOString(),
          method: 'manual',
          markedBy,
          exitTime: new Date().toISOString(),
          exitMethod: 'manual',
          exitMarkedBy: markedBy
        }
        attendanceRecords.push(exitRecord)
        localStorage.setItem(`workshop_attendance_${workshopId}`, JSON.stringify(attendanceRecords))
      }

      // إشعار المدرب
      await this.notifyTrainerOfExit(workshopId, { name: participantName }, 'manual')

      return { participantName, exitTime: new Date().toISOString() }
    } catch (error) {
      console.error('Error marking exit manually:', error)
      throw error
    }
  }

  // إنشاء كود حضور جديد
  generateAttendanceCode(workshopId, expiryMinutes = 30) {
    try {
      const code = Math.random().toString(36).substring(2, 8).toUpperCase()
      const expiry = new Date(Date.now() + expiryMinutes * 60 * 1000)
      
      localStorage.setItem(`workshop_code_${workshopId}`, code)
      localStorage.setItem(`workshop_code_expiry_${workshopId}`, expiry.toISOString())
      
      return { code, expiry }
    } catch (error) {
      console.error('Error generating attendance code:', error)
      throw error
    }
  }

  // إلغاء كود الحضور
  deactivateAttendanceCode(workshopId) {
    try {
      localStorage.removeItem(`workshop_code_${workshopId}`)
      localStorage.removeItem(`workshop_code_expiry_${workshopId}`)
      return true
    } catch (error) {
      console.error('Error deactivating attendance code:', error)
      throw error
    }
  }

  // الحصول على إحصائيات الحضور
  getAttendanceStats(workshopId, participants = []) {
    try {
      const attendanceRecords = JSON.parse(localStorage.getItem(`workshop_attendance_${workshopId}`) || '[]')
      
      const present = attendanceRecords.filter(record => record.status === 'present').length
      const absent = attendanceRecords.filter(record => record.status === 'absent').length
      const total = participants.length
      const notMarked = total - present - absent

      return {
        present,
        absent,
        notMarked,
        total,
        attendanceRate: total > 0 ? Math.round((present / total) * 100) : 0
      }
    } catch (error) {
      console.error('Error getting attendance stats:', error)
      return { present: 0, absent: 0, notMarked: 0, total: 0, attendanceRate: 0 }
    }
  }

  // الحصول على سجل حضور متدرب معين
  getTraineeAttendanceHistory(traineeName) {
    try {
      const workshops = JSON.parse(localStorage.getItem('app_workshops') || '[]')
      const userWorkshops = workshops.filter(workshop =>
        (workshop.participants && workshop.participants.includes(traineeName)) ||
        (workshop.selectedTrainees && workshop.selectedTrainees.includes(traineeName))
      )

      return userWorkshops.map(workshop => {
        const attendanceRecords = JSON.parse(localStorage.getItem(`workshop_attendance_${workshop.id}`) || '[]')
        const userAttendance = attendanceRecords.find(record => 
          record.participantName === traineeName
        )

        return {
          workshop,
          attendance: userAttendance,
          status: userAttendance ? userAttendance.status : 'not_marked'
        }
      })
    } catch (error) {
      console.error('Error getting trainee attendance history:', error)
      return []
    }
  }

  // إشعار المدرب بتسجيل حضور
  async notifyTrainerOfAttendance(workshopId, user, method) {
    try {
      if (!this.notificationService) return

      const workshops = JSON.parse(localStorage.getItem('app_workshops') || '[]')
      const workshop = workshops.find(w => w.id === workshopId)
      
      if (!workshop) return

      const message = `تم تسجيل حضور ${user.name} في ورشة "${workshop.title}" عبر ${method === 'code' ? 'كود الحضور' : 'التسجيل اليدوي'}`
      
      await this.notificationService.sendNotification({
        title: 'تسجيل حضور جديد',
        message,
        type: 'attendance',
        recipientRole: 'trainer',
        workshopId,
        timestamp: new Date().toISOString()
      })
    } catch (error) {
      console.error('Error notifying trainer:', error)
    }
  }

  // إشعار المدرب بتسجيل الخروج
  async notifyTrainerOfExit(workshopId, user, method) {
    try {
      if (!this.notificationService) return

      const workshops = JSON.parse(localStorage.getItem('app_workshops') || '[]')
      const workshop = workshops.find(w => w.id === workshopId)

      if (!workshop) return

      const message = `تم تسجيل خروج ${user.name} من ورشة "${workshop.title}" عبر ${method === 'code' ? 'كود الخروج' : 'التسجيل اليدوي'}`

      await this.notificationService.sendNotification({
        title: 'تسجيل خروج',
        message,
        type: 'exit',
        recipientRole: 'trainer',
        workshopId,
        timestamp: new Date().toISOString()
      })
    } catch (error) {
      console.error('Error notifying trainer of exit:', error)
    }
  }

  // إشعار المتدرب بتسجيل الحضور
  async notifyTraineeOfAttendance(traineeName, status, workshopId) {
    try {
      if (!this.notificationService) return

      const workshops = JSON.parse(localStorage.getItem('app_workshops') || '[]')
      const workshop = workshops.find(w => w.id === workshopId)
      
      if (!workshop) return

      const message = status === 'present' 
        ? `تم تسجيل حضورك في ورشة "${workshop.title}" بنجاح`
        : `تم تسجيل غيابك في ورشة "${workshop.title}"`
      
      await this.notificationService.sendNotification({
        title: 'تحديث حالة الحضور',
        message,
        type: 'attendance',
        recipientName: traineeName,
        workshopId,
        timestamp: new Date().toISOString()
      })
    } catch (error) {
      console.error('Error notifying trainee:', error)
    }
  }

  // تصدير تقرير حضور شامل لجميع ورش العمل
  async exportComprehensiveAttendanceReport() {
    try {
      const ExcelJS = (await import('exceljs')).default
      const { saveAs } = await import('file-saver')

      const workbook = new ExcelJS.Workbook()
      workbook.creator = 'نظام إدارة التدريب'
      workbook.created = new Date()

      // الحصول على جميع ورش العمل
      const workshops = JSON.parse(localStorage.getItem('app_workshops') || '[]')
      const users = JSON.parse(localStorage.getItem('app_users') || '[]')
      const trainees = users.filter(user => user.role === 'trainee')

      // إنشاء ورقة ملخص الحضور
      const summarySheet = workbook.addWorksheet('ملخص الحضور العام')

      // عنوان التقرير
      summarySheet.mergeCells('A1:F1')
      const titleCell = summarySheet.getCell('A1')
      titleCell.value = 'تقرير الحضور الشامل - جميع ورش العمل'
      titleCell.font = { size: 16, bold: true }
      titleCell.alignment = { horizontal: 'center' }

      // رؤوس الأعمدة
      const headers = ['المتدرب', 'إجمالي ورش العمل', 'حضر', 'غاب', 'لم يسجل', 'معدل الحضور']
      headers.forEach((header, index) => {
        const cell = summarySheet.getCell(3, index + 1)
        cell.value = header
        cell.font = { bold: true }
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'FFE6E6FA' }
        }
      })

      // بيانات المتدربين
      trainees.forEach((trainee, index) => {
        const traineeWorkshops = workshops.filter(workshop =>
          (workshop.participants && workshop.participants.includes(trainee.name)) ||
          (workshop.selectedTrainees && workshop.selectedTrainees.includes(trainee.name))
        )

        let present = 0, absent = 0, notMarked = 0

        traineeWorkshops.forEach(workshop => {
          const attendanceRecords = JSON.parse(localStorage.getItem(`workshop_attendance_${workshop.id}`) || '[]')
          const userAttendance = attendanceRecords.find(record =>
            record.participantName === trainee.name || record.participantId === trainee.id
          )

          if (userAttendance) {
            if (userAttendance.status === 'present') present++
            else if (userAttendance.status === 'absent') absent++
          } else {
            notMarked++
          }
        })

        const attendanceRate = traineeWorkshops.length > 0
          ? Math.round((present / traineeWorkshops.length) * 100)
          : 0

        const rowData = [
          trainee.name,
          traineeWorkshops.length,
          present,
          absent,
          notMarked,
          `${attendanceRate}%`
        ]

        rowData.forEach((value, colIndex) => {
          const cell = summarySheet.getCell(index + 4, colIndex + 1)
          cell.value = value
          cell.alignment = { horizontal: 'center' }

          // تلوين معدل الحضور
          if (colIndex === 5) {
            if (attendanceRate >= 90) {
              cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFE6FFE6' } }
            } else if (attendanceRate >= 70) {
              cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFFFFF99' } }
            } else {
              cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFFFE6E6' } }
            }
          }
        })
      })

      // تنسيق الأعمدة
      summarySheet.columns.forEach(column => {
        column.width = 20
      })

      // إضافة ورقة لكل ورشة عمل
      workshops.forEach(workshop => {
        const workshopSheet = workbook.addWorksheet(workshop.title.substring(0, 30))

        // عنوان الورشة
        workshopSheet.mergeCells('A1:F1')
        const workshopTitleCell = workshopSheet.getCell('A1')
        workshopTitleCell.value = `سجل حضور: ${workshop.title}`
        workshopTitleCell.font = { size: 14, bold: true }
        workshopTitleCell.alignment = { horizontal: 'center' }

        // رؤوس الأعمدة
        const workshopHeaders = ['المتدرب', 'الحالة', 'وقت التسجيل', 'طريقة التسجيل', 'مسجل بواسطة', 'ملاحظات']
        workshopHeaders.forEach((header, index) => {
          const cell = workshopSheet.getCell(3, index + 1)
          cell.value = header
          cell.font = { bold: true }
          cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'FFE6E6FA' }
          }
        })

        // بيانات الحضور للورشة
        const participants = workshop.participants || workshop.selectedTrainees || []
        const attendanceRecords = JSON.parse(localStorage.getItem(`workshop_attendance_${workshop.id}`) || '[]')

        participants.forEach((participant, index) => {
          const participantName = typeof participant === 'object' ? participant.name : participant
          const record = attendanceRecords.find(r => r.participantName === participantName)

          const rowData = [
            participantName,
            record ? (record.status === 'present' ? 'حاضر ✅' : 'غائب ❌') : 'لم يتم التسجيل ⏳',
            record ? new Date(record.timestamp).toLocaleString('ar-SA') : '-',
            record ? (record.method === 'code' ? 'كود الحضور' : 'يدوي') : '-',
            record ? record.markedBy || 'النظام' : '-',
            record ? record.notes || '-' : '-'
          ]

          rowData.forEach((value, colIndex) => {
            const cell = workshopSheet.getCell(index + 4, colIndex + 1)
            cell.value = value
            cell.alignment = { horizontal: 'center' }

            // تلوين الحالة
            if (colIndex === 1) {
              if (record?.status === 'present') {
                cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFE6FFE6' } }
              } else if (record?.status === 'absent') {
                cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFFFE6E6' } }
              }
            }
          })
        })

        // تنسيق أعمدة الورشة
        workshopSheet.columns.forEach(column => {
          column.width = 20
        })
      })

      // حفظ الملف
      const buffer = await workbook.xlsx.writeBuffer()
      const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
      const fileName = `تقرير_الحضور_الشامل_${new Date().toISOString().split('T')[0]}.xlsx`

      saveAs(blob, fileName)
      return true
    } catch (error) {
      console.error('Error exporting comprehensive attendance report:', error)
      throw error
    }
  }

  // تصدير سجل الحضور إلى Excel
  async exportAttendanceToExcel(workshopId, workshopTitle, participants) {
    try {
      const ExcelJS = (await import('exceljs')).default
      const { saveAs } = await import('file-saver')

      const workbook = new ExcelJS.Workbook()
      const worksheet = workbook.addWorksheet('سجل الحضور')

      // عنوان التقرير
      worksheet.mergeCells('A1:H1')
      const titleCell = worksheet.getCell('A1')
      titleCell.value = `سجل حضور ورشة العمل: ${workshopTitle}`
      titleCell.font = { size: 16, bold: true, color: { argb: 'FFFFFFFF' } }
      titleCell.alignment = { horizontal: 'center', vertical: 'middle' }
      titleCell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FF3B82F6' } // اللون الأساسي للموقع (أزرق)
      }
      titleCell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' }
      }

      // معلومات إضافية
      worksheet.getCell('A2').value = `تاريخ التصدير: ${new Date().toLocaleDateString('ar-SA')}`
      worksheet.getCell('A2').font = { italic: true }

      // رؤوس الأعمدة
      const headers = ['اسم المتدرب', 'حالة الحضور', 'وقت الحضور', 'طريقة الحضور', 'وقت الخروج', 'طريقة الخروج', 'مسجل بواسطة', 'ملاحظات']
      headers.forEach((header, index) => {
        const cell = worksheet.getCell(3, index + 1)
        cell.value = header
        cell.font = { bold: true, color: { argb: 'FFFFFFFF' } }
        cell.alignment = { horizontal: 'center', vertical: 'middle' }
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'FF6366F1' } // اللون الثانوي للموقع (بنفسجي)
        }
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' }
        }
      })

      // بيانات الحضور
      const attendanceRecords = JSON.parse(localStorage.getItem(`workshop_attendance_${workshopId}`) || '[]')
      
      participants.forEach((participant, index) => {
        const record = attendanceRecords.find(r => r.participantName === participant.name || r.participantName === participant)
        const participantName = typeof participant === 'object' ? participant.name : participant

        const rowData = [
          participantName,
          record ? (record.status === 'present' ? 'حاضر ✅' : record.status === 'absent' ? 'غائب ❌' : 'خروج 🚪') : 'لم يتم التسجيل ⏳',
          record ? new Date(record.timestamp).toLocaleString('ar-SA') : '-',
          record ? (record.method === 'code' ? 'كود الحضور' : 'يدوي') : '-',
          record?.exitTime ? new Date(record.exitTime).toLocaleString('ar-SA') : '-',
          record?.exitMethod ? (record.exitMethod === 'code' ? 'كود الخروج' : 'يدوي') : '-',
          record ? (record.markedBy || record.exitMarkedBy || 'النظام') : '-',
          record ? record.notes || '-' : '-'
        ]

        rowData.forEach((value, colIndex) => {
          const cell = worksheet.getCell(index + 4, colIndex + 1)
          cell.value = value
          cell.alignment = { horizontal: 'center', vertical: 'middle' }
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' }
          }

          // تلوين الصفوف حسب الحالة
          if (colIndex === 1) {
            if (record?.status === 'present') {
              cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF10B981' } } // أخضر
              cell.font = { color: { argb: 'FFFFFFFF' }, bold: true }
            } else if (record?.status === 'absent') {
              cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFEF4444' } } // أحمر
              cell.font = { color: { argb: 'FFFFFFFF' }, bold: true }
            } else if (record?.status === 'exit') {
              cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFF59E0B' } } // برتقالي
              cell.font = { color: { argb: 'FFFFFFFF' }, bold: true }
            }
          }
        })
      })

      // تنسيق الأعمدة
      worksheet.columns.forEach(column => {
        column.width = 20
      })

      // حفظ الملف
      const buffer = await workbook.xlsx.writeBuffer()
      const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
      const fileName = `حضور_${workshopTitle}_${new Date().toISOString().split('T')[0]}.xlsx`

      saveAs(blob, fileName)
      return true
    } catch (error) {
      console.error('Error exporting attendance to Excel:', error)
      throw error
    }
  }
}

// إنشاء instance واحد للخدمة
const attendanceService = new AttendanceService()

export default attendanceService
