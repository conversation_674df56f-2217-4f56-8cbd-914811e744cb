import React, { useState, useEffect } from 'react'
import { Link, useNavigate } from 'react-router-dom'
import { motion } from 'framer-motion'
import {
  Clock,
  Calendar,
  CheckSquare,
  BookOpen,
  Users,
  Award,
  TrendingUp,
  Play,
  Download,
  MessageSquare,
  RotateCw,
  Target,
  Star,
  Activity,
  Bell,
  ChevronRight,
  Plus,
  Eye,
  BarChart3,
  Zap,
  Timer,
  CheckCircle,
  AlertCircle,
  Search,
  Filter,
  CalendarDays,
  GraduationCap,
  FileText,
  UserCheck
} from 'lucide-react'
import { useLanguage } from '../../contexts/LanguageContext'
import { useAuth } from '../../contexts/AuthContext'
// تم إزالة استيراد دالة التنظيف التلقائي للبيانات الوهمية
import Button from '../../components/ui/Button'

const TraineeDashboard = () => {
  const { t } = useLanguage()
  const { user } = useAuth()
  const navigate = useNavigate()
  const [dashboardData, setDashboardData] = useState({
    coursesEnrolled: 0,
    totalTasks: 0,
    tasksCompleted: 0,
    totalWorkshops: 0,
    workshopsAttended: 0,
    pointsEarned: 0,
    upcomingTasks: [],
    upcomingWorkshops: [],
    recentAchievements: [],
    unreadDiscussions: 0,
    personalTasks: 0,
    completionRate: 0,
    weeklyProgress: 0
  })
  const [loading, setLoading] = useState(true)

  // تحميل بيانات المتدرب
  useEffect(() => {
    if (user?.name) {
      // تم إزالة دالة التنظيف التلقائي للبيانات الوهمية
      loadTraineeData()
    }
  }, [user])

  // دالة تحميل البيانات الحقيقية
  const loadTraineeData = async () => {
    try {
      setLoading(true)
      
      // تحميل المهام المخصصة للمتدرب
      const allTasks = JSON.parse(localStorage.getItem('app_tasks') || '[]')
      const userTasks = allTasks.filter(task => 
        task.assignedUsers?.includes(user.name) || 
        task.assignedUsers?.includes('الكل') ||
        task.assignedUsers?.includes(user.id)
      )
      
      // تحميل ورش العمل
      const allWorkshops = JSON.parse(localStorage.getItem('app_workshops') || '[]')
      const userWorkshops = allWorkshops.filter(workshop => 
        workshop.participants?.includes(user.name) ||
        workshop.selectedTrainees?.includes(user.name) ||
        workshop.participants?.some(p => p.name === user.name || p.id === user.id)
      )
      
      // تحميل الدورات
      const allCourses = JSON.parse(localStorage.getItem('app_courses') || '[]')
      const userCourses = allCourses.filter(course => 
        course.selectedTrainees?.includes(user.name) ||
        course.enrolledStudents?.includes(user.name)
      )
      
      // تحميل المهام الشخصية
      const personalTasksKey = `personal_tasks_${user.id}`
      const personalTasks = JSON.parse(localStorage.getItem(personalTasksKey) || '[]')
      
      // حساب الإحصائيات
      const completedTasks = userTasks.filter(task => task.status === 'completed').length
      const completedWorkshops = userWorkshops.filter(workshop => workshop.status === 'completed').length
      const completionRate = userTasks.length > 0 ? Math.round((completedTasks / userTasks.length) * 100) : 0
      
      // المهام القادمة (خلال الأسبوع القادم)
      const nextWeek = new Date()
      nextWeek.setDate(nextWeek.getDate() + 7)
      const upcomingTasks = userTasks.filter(task => {
        const dueDate = new Date(task.dueDate)
        return dueDate <= nextWeek && task.status !== 'completed'
      }).slice(0, 5)
      
      // ورش العمل القادمة
      const upcomingWorkshops = userWorkshops.filter(workshop => {
        const workshopDate = new Date(workshop.date)
        return workshopDate >= new Date() && workshop.status !== 'completed'
      }).slice(0, 3)
      
      setDashboardData({
        coursesEnrolled: userCourses.length,
        totalTasks: userTasks.length,
        tasksCompleted: completedTasks,
        totalWorkshops: userWorkshops.length,
        workshopsAttended: completedWorkshops,
        personalTasks: personalTasks.length,
        completionRate,
        upcomingTasks,
        upcomingWorkshops,
        pointsEarned: (completedTasks * 10) + (completedWorkshops * 15),
        weeklyProgress: Math.min(100, (completedTasks + completedWorkshops) * 5),
        recentAchievements: [],
        unreadDiscussions: 0
      })
      
    } catch (error) {
      console.error('خطأ في تحميل بيانات المتدرب:', error)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">جاري تحميل لوحة التحكم...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">

        {/* Header with Personal Schedule */}
        <div className="mb-8">
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-sm border border-gray-200 dark:border-gray-700"
          >
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center space-x-3 rtl:space-x-reverse">
                <div className="p-3 bg-green-100 dark:bg-green-900/30 rounded-xl">
                  <Calendar className="w-6 h-6 text-green-600 dark:text-green-400" />
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                    {t('timeline.personalSchedule')}
                  </h1>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    مرحباً {user?.name || 'المتدرب'} - إدارة الأنشطة والمهام والتقييم الزمني
                  </p>
                </div>
              </div>
              <div className="text-left rtl:text-right">
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {new Date().toLocaleDateString('ar-SA', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  })}
                </p>
                <div className="flex items-center mt-1">
                  <div className="w-2 h-2 bg-green-500 rounded-full ml-2"></div>
                  <span className="text-xs text-green-600 dark:text-green-400">متصل الآن</span>
                </div>
              </div>
            </div>
          </motion.div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-2 lg:grid-cols-5 gap-4 mb-8">
          {/* المهام المكتملة */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="bg-white dark:bg-gray-800 rounded-xl p-4 shadow-sm border border-gray-200 dark:border-gray-700 cursor-pointer hover:shadow-md transition-all duration-300"
            onClick={() => navigate('/assignments')}
          >
            <div className="flex items-center justify-between mb-3">
              <div className="p-2 bg-green-100 dark:bg-green-900/30 rounded-lg">
                <Award className="w-5 h-5 text-green-600 dark:text-green-400" />
              </div>
              <span className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {dashboardData.tasksCompleted}
              </span>
            </div>
            <p className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              {t('dashboard.completed') || 'مكتمل'}
            </p>
            <p className="text-xs text-gray-500 dark:text-gray-400">
              {t('dashboard.completedTask') || 'مهمة منجزة'}
            </p>
          </motion.div>

          {/* المهام المعلقة */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="bg-white dark:bg-gray-800 rounded-xl p-4 shadow-sm border border-gray-200 dark:border-gray-700 cursor-pointer hover:shadow-md transition-all duration-300"
            onClick={() => navigate('/assignments')}
          >
            <div className="flex items-center justify-between mb-3">
              <div className="p-2 bg-orange-100 dark:bg-orange-900/30 rounded-lg">
                <Timer className="w-5 h-5 text-orange-600 dark:text-orange-400" />
              </div>
              <span className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {dashboardData.totalTasks - dashboardData.tasksCompleted}
              </span>
            </div>
            <p className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              {t('dashboard.pending') || 'معلق'}
            </p>
            <p className="text-xs text-gray-500 dark:text-gray-400">
              {t('dashboard.pendingTask') || 'مهمة متبقية'}
            </p>
          </motion.div>

          {/* ورش العمل */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="bg-white dark:bg-gray-800 rounded-xl p-4 shadow-sm border border-gray-200 dark:border-gray-700 cursor-pointer hover:shadow-md transition-all duration-300"
            onClick={() => navigate('/workshop')}
          >
            <div className="flex items-center justify-between mb-3">
              <div className="p-2 bg-purple-100 dark:bg-purple-900/30 rounded-lg">
                <Zap className="w-5 h-5 text-purple-600 dark:text-purple-400" />
              </div>
              <span className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {dashboardData.totalWorkshops}
              </span>
            </div>
            <p className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              {t('dashboard.workshops') || 'ورش العمل'}
            </p>
            <p className="text-xs text-gray-500 dark:text-gray-400">
              {t('dashboard.availableWorkshop') || 'ورشة متاحة'}
            </p>
          </motion.div>

          {/* الأيام التدريبية */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="bg-white dark:bg-gray-800 rounded-xl p-4 shadow-sm border border-gray-200 dark:border-gray-700 cursor-pointer hover:shadow-md transition-all duration-300"
            onClick={() => navigate('/timeline')}
          >
            <div className="flex items-center justify-between mb-3">
              <div className="p-2 bg-indigo-100 dark:bg-indigo-900/30 rounded-lg">
                <TrendingUp className="w-5 h-5 text-indigo-600 dark:text-indigo-400" />
              </div>
              <span className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {dashboardData.completionRate}
              </span>
            </div>
            <p className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              معدل الإنجاز
            </p>
            <p className="text-xs text-gray-500 dark:text-gray-400">
              نسبة إكمال المهام %
            </p>
          </motion.div>

          {/* الدورات (الروتيشن) - نقل إلى اليمين */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
            className="bg-white dark:bg-gray-800 rounded-xl p-4 shadow-sm border border-gray-200 dark:border-gray-700 cursor-pointer hover:shadow-md transition-all duration-300"
            onClick={() => navigate('/my-courses')}
          >
            <div className="flex items-center justify-between mb-3">
              <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                <RotateCw className="w-5 h-5 text-blue-600 dark:text-blue-400" />
              </div>
              <span className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {dashboardData.coursesEnrolled}
              </span>
            </div>
            <p className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              {t('dashboard.courses') || 'الدورات (الروتيشن)'}
            </p>
            <p className="text-xs text-gray-500 dark:text-gray-400">
              {t('dashboard.enrolledCourse') || 'دورة مسجلة'}
            </p>
          </motion.div>
        </div>

        {/* Search and Filter Section */}
        <div className="mb-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 }}
            className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700"
          >
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                  <input
                    type="text"
                    placeholder={t('timeline.search')}
                    className="w-full pr-10 pl-4 py-3 border border-gray-200 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>
              <div className="flex items-center gap-3">
                <select className="px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-blue-500">
                  <option value="all">جميع الأحداث</option>
                  <option value="tasks">المهام</option>
                  <option value="workshops">ورش العمل</option>
                  <option value="courses">الدورات</option>
                </select>
                <Button
                  variant="outline"
                  icon={Filter}
                  className="px-4 py-3"
                >
                  تصفية
                </Button>
              </div>
            </div>
          </motion.div>
        </div>

        {/* Upcoming and Current Events */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* الدورات القادمة */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.7 }}
            className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700"
          >
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                الدورات القادمة (الروتيشن)
              </h3>
              <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                <RotateCw className="w-5 h-5 text-blue-600 dark:text-blue-400" />
              </div>
            </div>

            <div className="space-y-4">
              {dashboardData.upcomingWorkshops.length > 0 ? (
                dashboardData.upcomingWorkshops.slice(0, 3).map((course, index) => (
                  <div key={index} className="flex items-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors cursor-pointer">
                    <div className="p-2 bg-green-100 dark:bg-green-900/30 rounded-lg ml-3">
                      <GraduationCap className="w-4 h-4 text-green-600 dark:text-green-400" />
                    </div>
                    <div className="flex-1">
                      <h4 className="font-medium text-gray-900 dark:text-gray-100 text-sm">
                        {course.title || course.courseName}
                      </h4>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        تاريخ البداية: {new Date(course.startDate).toLocaleDateString('ar-SA')}
                      </p>
                    </div>
                    <div className="text-xs text-blue-600 dark:text-blue-400 font-medium">
                      قريباً
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-8">
                  <RotateCw className="w-12 h-12 text-gray-400 mx-auto mb-3" />
                  <p className="text-gray-500 dark:text-gray-400">لا توجد دورات قادمة</p>
                </div>
              )}
            </div>

            {dashboardData.upcomingWorkshops.length > 3 && (
              <div className="mt-4 text-center">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => navigate('/courses')}
                >
                  عرض جميع الدورات
                </Button>
              </div>
            )}
          </motion.div>

          {/* المهام القادمة */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.8 }}
            className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700"
          >
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                المهام القادمة
              </h3>
              <div className="p-2 bg-orange-100 dark:bg-orange-900/30 rounded-lg">
                <CheckSquare className="w-5 h-5 text-orange-600 dark:text-orange-400" />
              </div>
            </div>

            <div className="space-y-4">
              {dashboardData.upcomingTasks.length > 0 ? (
                dashboardData.upcomingTasks.slice(0, 3).map((task, index) => (
                  <div key={index} className="flex items-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors cursor-pointer">
                    <div className="p-2 bg-red-100 dark:bg-red-900/30 rounded-lg ml-3">
                      <Target className="w-4 h-4 text-red-600 dark:text-red-400" />
                    </div>
                    <div className="flex-1">
                      <h4 className="font-medium text-gray-900 dark:text-gray-100 text-sm">
                        {task.title || task.taskName}
                      </h4>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        موعد التسليم: {new Date(task.dueDate).toLocaleDateString('ar-SA')}
                      </p>
                    </div>
                    <div className="text-xs text-red-600 dark:text-red-400 font-medium">
                      عاجل
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-8">
                  <CheckSquare className="w-12 h-12 text-gray-400 mx-auto mb-3" />
                  <p className="text-gray-500 dark:text-gray-400">لا توجد مهام قادمة</p>
                </div>
              )}
            </div>

            {dashboardData.upcomingTasks.length > 3 && (
              <div className="mt-4 text-center">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => navigate('/assignments')}
                >
                  عرض جميع المهام
                </Button>
              </div>
            )}
          </motion.div>

          {/* لا توجد أحداث */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.8 }}
            className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700"
          >
            <div className="text-center py-12">
              <div className="p-4 bg-gray-100 dark:bg-gray-700 rounded-full w-20 h-20 mx-auto mb-4 flex items-center justify-center">
                <Calendar className="w-10 h-10 text-gray-400" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                لا توجد أحداث
              </h3>
              <p className="text-gray-500 dark:text-gray-400 text-sm mb-6">
                لا توجد أحداث مجدولة في الوقت الحالي
              </p>
              <Button
                onClick={() => navigate('/timeline')}
                variant="primary"
                icon={Plus}
                size="sm"
              >
                عرض التايم لاين
              </Button>
            </div>
          </motion.div>
        </div>

        {/* Quick Actions */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.9 }}
          className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700"
        >
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6">
            {t('dashboard.quickActions') || 'إجراءات سريعة'}
          </h3>
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
            <Button
              onClick={() => navigate('/assignments')}
              variant="outline"
              icon={Target}
              className="h-20 flex-col"
            >
              <span className="mt-2 text-sm">{t('dashboard.tasks') || 'المهام'}</span>
            </Button>
            <Button
              onClick={() => navigate('/workshop')}
              variant="outline"
              icon={Zap}
              className="h-20 flex-col"
            >
              <span className="mt-2 text-sm">{t('dashboard.workshops') || 'ورش العمل'}</span>
            </Button>
            <Button
              onClick={() => navigate('/timeline')}
              variant="outline"
              icon={Calendar}
              className="h-20 flex-col"
            >
              <span className="mt-2 text-sm">{t('dashboard.timeline') || 'التايم لاين'}</span>
            </Button>
            <Button
              onClick={() => navigate('/my-courses')}
              variant="outline"
              icon={RotateCw}
              className="h-20 flex-col"
            >
              <span className="mt-2 text-sm">{t('dashboard.courses') || 'دوراتي (الروتيشن)'}</span>
            </Button>
          </div>
        </motion.div>
      </div>
    </div>
  )
}

export default TraineeDashboard
