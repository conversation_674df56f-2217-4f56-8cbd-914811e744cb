import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { 
  Qr<PERSON>ode, 
  CheckCircle, 
  XCircle, 
  Clock,
  User,
  Calendar,
  LogOut
} from 'lucide-react'
import { toast } from 'react-hot-toast'
import Button from '../ui/Button'
import { useAuth } from '../../contexts/AuthContext'
import attendanceService from '../../services/attendanceService'

const ExitCodeInput = ({ workshopId, workshopTitle, onExitMarked }) => {
  const { user } = useAuth()
  const [code, setCode] = useState('')
  const [loading, setLoading] = useState(false)
  const [exitStatus, setExitStatus] = useState(null)

  React.useEffect(() => {
    checkExistingExit()
  }, [workshopId, user])

  const checkExistingExit = () => {
    try {
      const attendanceRecords = JSON.parse(localStorage.getItem(`workshop_attendance_${workshopId}`) || '[]')
      const userRecord = attendanceRecords.find(record => 
        record.participantName === user.name || record.participantId === user.id
      )

      if (userRecord && userRecord.exitTime) {
        setExitStatus('exited')
      } else {
        setExitStatus(null)
      }
    } catch (error) {
      console.error('Error checking existing exit:', error)
    }
  }

  const submitExitCode = async () => {
    if (!code.trim()) {
      toast.error('يرجى إدخال كود الخروج')
      return
    }

    setLoading(true)

    try {
      const record = await attendanceService.markExitByCode(workshopId, code, user)

      setExitStatus('exited')
      setCode('')

      toast.success('تم تسجيل الخروج بنجاح!')

      // إشعار المكون الأب
      if (onExitMarked) {
        onExitMarked(record)
      }

    } catch (error) {
      console.error('Error submitting exit code:', error)
      toast.error(error.message || 'حدث خطأ أثناء تسجيل الخروج')
    } finally {
      setLoading(false)
    }
  }

  const handleCodeChange = (e) => {
    const value = e.target.value.toUpperCase().replace(/[^A-Z0-9]/g, '')
    if (value.length <= 6) {
      setCode(value)
    }
  }

  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      submitExitCode()
    }
  }

  if (exitStatus === 'exited') {
    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-700 rounded-lg p-6"
      >
        <div className="flex items-center justify-center mb-4">
          <div className="w-16 h-16 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center">
            <CheckCircle className="w-8 h-8 text-green-600 dark:text-green-400" />
          </div>
        </div>
        <div className="text-center">
          <h3 className="text-lg font-semibold text-green-900 dark:text-green-100 mb-2">
            تم تسجيل الخروج بنجاح
          </h3>
          <p className="text-green-700 dark:text-green-300 text-sm">
            تم تسجيل خروجك من ورشة العمل "{workshopTitle}"
          </p>
          <div className="mt-4 flex items-center justify-center text-sm text-green-600 dark:text-green-400">
            <Clock className="w-4 h-4 mr-2 rtl:mr-0 rtl:ml-2" />
            <span>وقت الخروج: {new Date().toLocaleTimeString('ar-SA')}</span>
          </div>
        </div>
      </motion.div>
    )
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6"
    >
      <div className="text-center mb-6">
        <div className="w-16 h-16 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
          <LogOut className="w-8 h-8 text-red-600 dark:text-red-400" />
        </div>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
          تسجيل الخروج
        </h3>
        <p className="text-gray-600 dark:text-gray-400 text-sm">
          أدخل كود الخروج المقدم من المدرب لتسجيل خروجك من ورشة العمل
        </p>
      </div>

      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            كود الخروج
          </label>
          <input
            type="text"
            value={code}
            onChange={handleCodeChange}
            onKeyPress={handleKeyPress}
            placeholder="أدخل كود الخروج (6 أحرف/أرقام)"
            className="w-full px-4 py-3 text-center text-2xl font-mono tracking-widest border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500 dark:bg-gray-700 dark:text-gray-100 uppercase"
            maxLength={6}
            disabled={loading}
          />
        </div>

        <Button
          onClick={submitExitCode}
          disabled={!code.trim() || loading}
          loading={loading}
          className="w-full bg-red-600 hover:bg-red-700 text-white"
          icon={LogOut}
        >
          {loading ? 'جاري تسجيل الخروج...' : 'تسجيل الخروج'}
        </Button>
      </div>

      <div className="mt-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
        <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
          <User className="w-4 h-4 mr-2 rtl:mr-0 rtl:ml-2" />
          <span>المتدرب: {user.name}</span>
        </div>
        <div className="flex items-center text-sm text-gray-600 dark:text-gray-400 mt-2">
          <Calendar className="w-4 h-4 mr-2 rtl:mr-0 rtl:ml-2" />
          <span>ورشة العمل: {workshopTitle}</span>
        </div>
      </div>

      <div className="mt-4 text-center">
        <p className="text-xs text-gray-500 dark:text-gray-400">
          تأكد من الحصول على كود الخروج الصحيح من المدرب قبل المغادرة
        </p>
      </div>
    </motion.div>
  )
}

export default ExitCodeInput
