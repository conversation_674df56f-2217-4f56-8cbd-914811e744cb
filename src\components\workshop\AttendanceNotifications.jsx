import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Bell, 
  CheckCircle, 
  XCircle, 
  Clock, 
  User,
  X
} from 'lucide-react'
import { toast } from 'react-hot-toast'

const AttendanceNotifications = ({ workshopId, onNotificationRead }) => {
  const [notifications, setNotifications] = useState([])
  const [showNotifications, setShowNotifications] = useState(false)

  useEffect(() => {
    loadNotifications()
    const interval = setInterval(loadNotifications, 5000) // تحديث كل 5 ثوان
    return () => clearInterval(interval)
  }, [workshopId])

  const loadNotifications = () => {
    try {
      const attendanceRecords = JSON.parse(localStorage.getItem(`workshop_attendance_${workshopId}`) || '[]')
      const readNotifications = JSON.parse(localStorage.getItem(`read_notifications_${workshopId}`) || '[]')

      // الحصول على آخر 10 تسجيلات حضور
      const recentAttendance = attendanceRecords
        .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
        .slice(0, 10)
        .map(record => {
          const notificationId = `${record.participantName}_${record.timestamp}`
          return {
            id: notificationId,
            type: 'attendance',
            participantName: record.participantName,
            status: record.status,
            method: record.method,
            timestamp: record.timestamp,
            read: readNotifications.includes(notificationId)
          }
        })

      setNotifications(recentAttendance)
    } catch (error) {
      console.error('Error loading notifications:', error)
    }
  }

  const markAsRead = (notificationId) => {
    // حفظ الإشعار كمقروء في localStorage
    try {
      const readNotifications = JSON.parse(localStorage.getItem(`read_notifications_${workshopId}`) || '[]')
      if (!readNotifications.includes(notificationId)) {
        readNotifications.push(notificationId)
        localStorage.setItem(`read_notifications_${workshopId}`, JSON.stringify(readNotifications))
      }
    } catch (error) {
      console.error('Error saving read notification:', error)
    }

    setNotifications(prev =>
      prev.map(notif =>
        notif.id === notificationId
          ? { ...notif, read: true }
          : notif
      )
    )

    if (onNotificationRead) {
      onNotificationRead(notificationId)
    }
  }

  const clearAllNotifications = () => {
    setNotifications([])
    setShowNotifications(false)
  }

  const unreadCount = notifications.filter(n => !n.read).length

  const formatTimeAgo = (timestamp) => {
    const now = new Date()
    const time = new Date(timestamp)
    const diffInMinutes = Math.floor((now - time) / (1000 * 60))
    
    if (diffInMinutes < 1) return 'الآن'
    if (diffInMinutes < 60) return `منذ ${diffInMinutes} دقيقة`
    
    const diffInHours = Math.floor(diffInMinutes / 60)
    if (diffInHours < 24) return `منذ ${diffInHours} ساعة`
    
    const diffInDays = Math.floor(diffInHours / 24)
    return `منذ ${diffInDays} يوم`
  }

  return (
    <div className="relative">
      {/* Notification Bell */}
      <button
        onClick={() => setShowNotifications(!showNotifications)}
        className="relative p-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors"
      >
        <Bell className="h-6 w-6" />
        {unreadCount > 0 && (
          <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
            {unreadCount > 9 ? '9+' : unreadCount}
          </span>
        )}
      </button>

      {/* Notifications Dropdown */}
      <AnimatePresence>
        {showNotifications && (
          <motion.div
            initial={{ opacity: 0, y: -10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -10, scale: 0.95 }}
            className="absolute left-0 mt-2 w-80 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-50"
          >
            {/* Header */}
            <div className="p-4 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  إشعارات الحضور
                </h3>
                <div className="flex items-center space-x-2 rtl:space-x-reverse">
                  {notifications.length > 0 && (
                    <button
                      onClick={clearAllNotifications}
                      className="text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                    >
                      مسح الكل
                    </button>
                  )}
                  <button
                    onClick={() => setShowNotifications(false)}
                    className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-200"
                  >
                    <X className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>

            {/* Notifications List */}
            <div className="max-h-96 overflow-y-auto">
              {notifications.length === 0 ? (
                <div className="p-8 text-center text-gray-500 dark:text-gray-400">
                  <Bell className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>لا توجد إشعارات حضور</p>
                </div>
              ) : (
                <div className="divide-y divide-gray-200 dark:divide-gray-700">
                  {notifications.map((notification) => (
                    <motion.div
                      key={notification.id}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      className={`p-4 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer transition-colors ${
                        !notification.read ? 'bg-blue-50 dark:bg-blue-900/20' : ''
                      }`}
                      onClick={() => markAsRead(notification.id)}
                    >
                      <div className="flex items-start space-x-3 rtl:space-x-reverse">
                        <div className={`flex-shrink-0 p-2 rounded-full ${
                          notification.status === 'present'
                            ? 'bg-green-100 dark:bg-green-900/20'
                            : 'bg-red-100 dark:bg-red-900/20'
                        }`}>
                          {notification.status === 'present' ? (
                            <CheckCircle className="h-4 w-4 text-green-600 dark:text-green-400" />
                          ) : (
                            <XCircle className="h-4 w-4 text-red-600 dark:text-red-400" />
                          )}
                        </div>
                        
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center space-x-2 rtl:space-x-reverse">
                            <User className="h-3 w-3 text-gray-400" />
                            <span className="text-sm font-medium text-gray-900 dark:text-white truncate">
                              {notification.participantName}
                            </span>
                          </div>
                          
                          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                            {notification.status === 'present' ? 'سجل حضوره' : 'تم تسجيل غيابه'}
                            {notification.method === 'code' ? ' عبر كود الحضور' : ' يدوياً'}
                          </p>
                          
                          <div className="flex items-center space-x-1 rtl:space-x-reverse mt-2">
                            <Clock className="h-3 w-3 text-gray-400" />
                            <span className="text-xs text-gray-500 dark:text-gray-400">
                              {formatTimeAgo(notification.timestamp)}
                            </span>
                          </div>
                        </div>

                        {!notification.read && (
                          <div className="flex-shrink-0">
                            <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                          </div>
                        )}
                      </div>
                    </motion.div>
                  ))}
                </div>
              )}
            </div>

            {/* Footer */}
            {notifications.length > 0 && (
              <div className="p-3 border-t border-gray-200 dark:border-gray-700 text-center">
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  آخر {notifications.length} تسجيلات حضور
                </span>
              </div>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

export default AttendanceNotifications
