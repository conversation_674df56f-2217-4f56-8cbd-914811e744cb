import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  Users, 
  QrC<PERSON>, 
  UserCheck, 
  UserX, 
  Clock, 
  Calendar,
  Copy,
  RefreshCw,
  Download,
  Eye,
  CheckCircle,
  XCircle
} from 'lucide-react'
import { toast } from 'react-hot-toast'
import Button from '../ui/Button'
import { useAuth } from '../../contexts/AuthContext'
import { useLanguage } from '../../contexts/LanguageContext'
import attendanceService from '../../services/attendanceService'
import AttendanceNotifications from './AttendanceNotifications'
import AttendanceStats from './AttendanceStats'
import AttendanceReminders from './AttendanceReminders'

const AttendanceManager = ({ workshopId, workshopTitle, participants = [] }) => {
  const { user } = useAuth()
  const { t } = useLanguage()
  const [attendanceCode, setAttendanceCode] = useState('')
  const [exitCode, setExitCode] = useState('')
  const [attendanceRecords, setAttendanceRecords] = useState([])
  const [isCodeActive, setIsCodeActive] = useState(false)
  const [isExitCodeActive, setIsExitCodeActive] = useState(false)
  const [codeExpiry, setCodeExpiry] = useState(null)
  const [exitCodeExpiry, setExitCodeExpiry] = useState(null)
  const [selectedParticipants, setSelectedParticipants] = useState([])
  const [showManualAttendance, setShowManualAttendance] = useState(false)
  const [attendanceStats, setAttendanceStats] = useState({
    present: 0,
    absent: 0,
    total: 0
  })
  const [refreshTrigger, setRefreshTrigger] = useState(0)

  useEffect(() => {
    loadAttendanceData()
    const interval = setInterval(checkCodeExpiry, 1000)
    return () => clearInterval(interval)
  }, [workshopId])

  useEffect(() => {
    calculateStats()
  }, [attendanceRecords, participants])

  const loadAttendanceData = () => {
    try {
      const savedRecords = JSON.parse(localStorage.getItem(`workshop_attendance_${workshopId}`) || '[]')
      const savedCode = localStorage.getItem(`workshop_code_${workshopId}`)
      const savedExpiry = localStorage.getItem(`workshop_code_expiry_${workshopId}`)
      
      setAttendanceRecords(savedRecords)
      
      if (savedCode && savedExpiry) {
        const expiryTime = new Date(savedExpiry)
        if (expiryTime > new Date()) {
          setAttendanceCode(savedCode)
          setCodeExpiry(expiryTime)
          setIsCodeActive(true)
        }
      }
    } catch (error) {
      console.error('Error loading attendance data:', error)
    }
  }

  const saveAttendanceData = (records) => {
    try {
      localStorage.setItem(`workshop_attendance_${workshopId}`, JSON.stringify(records))
      setAttendanceRecords(records)
    } catch (error) {
      console.error('Error saving attendance data:', error)
      toast.error('فشل في حفظ بيانات الحضور')
    }
  }

  const generateAttendanceCode = () => {
    try {
      const { code, expiry } = attendanceService.generateAttendanceCode(workshopId, 30)

      setAttendanceCode(code)
      setCodeExpiry(expiry)
      setIsCodeActive(true)

      toast.success('تم إنشاء كود الحضور بنجاح!')
    } catch (error) {
      console.error('Error generating code:', error)
      toast.error('فشل في إنشاء كود الحضور')
    }
  }

  const generateExitCode = () => {
    try {
      const code = Math.random().toString(36).substring(2, 8).toUpperCase()
      const expiry = new Date(Date.now() + 30 * 60 * 1000) // 30 minutes

      setExitCode(code)
      setExitCodeExpiry(expiry)
      setIsExitCodeActive(true)

      // حفظ كود الخروج في localStorage
      localStorage.setItem(`workshop_exit_code_${workshopId}`, code)
      localStorage.setItem(`workshop_exit_code_expiry_${workshopId}`, expiry.toISOString())

      toast.success('تم إنشاء كود الخروج بنجاح!')
    } catch (error) {
      console.error('Error generating exit code:', error)
      toast.error('فشل في إنشاء كود الخروج')
    }
  }

  const deactivateCode = () => {
    try {
      attendanceService.deactivateAttendanceCode(workshopId)

      setIsCodeActive(false)
      setAttendanceCode('')
      setCodeExpiry(null)

      toast.success('تم إلغاء كود الحضور')
    } catch (error) {
      console.error('Error deactivating code:', error)
      toast.error('فشل في إلغاء كود الحضور')
    }
  }

  const deactivateExitCode = () => {
    try {
      localStorage.removeItem(`workshop_exit_code_${workshopId}`)
      localStorage.removeItem(`workshop_exit_code_expiry_${workshopId}`)

      setIsExitCodeActive(false)
      setExitCode('')
      setExitCodeExpiry(null)

      toast.success('تم إلغاء كود الخروج')
    } catch (error) {
      console.error('Error deactivating exit code:', error)
      toast.error('فشل في إلغاء كود الخروج')
    }
  }

  const checkCodeExpiry = () => {
    if (codeExpiry && new Date() > codeExpiry) {
      setIsCodeActive(false)
      setAttendanceCode('')
      setCodeExpiry(null)
      localStorage.removeItem(`workshop_code_${workshopId}`)
      localStorage.removeItem(`workshop_code_expiry_${workshopId}`)
    }

    if (exitCodeExpiry && new Date() > exitCodeExpiry) {
      setIsExitCodeActive(false)
      setExitCode('')
      setExitCodeExpiry(null)
      localStorage.removeItem(`workshop_exit_code_${workshopId}`)
      localStorage.removeItem(`workshop_exit_code_expiry_${workshopId}`)
    }
  }

  const copyCodeToClipboard = () => {
    navigator.clipboard.writeText(attendanceCode)
    toast.success('تم نسخ كود الحضور!')
  }

  const copyExitCodeToClipboard = () => {
    navigator.clipboard.writeText(exitCode)
    toast.success('تم نسخ كود الخروج!')
  }

  const markAttendanceManually = async (participantName, status) => {
    try {
      await attendanceService.markAttendanceManually(workshopId, participantName, status, user.name)
      loadAttendanceData() // إعادة تحميل البيانات
      setRefreshTrigger(prev => prev + 1) // تحديث الإحصائيات
      toast.success(`تم تسجيل ${status === 'present' ? 'حضور' : 'غياب'} ${participantName}`)
    } catch (error) {
      console.error('Error marking attendance:', error)
      toast.error('فشل في تسجيل الحضور')
    }
  }

  const markExitManually = async (participantName) => {
    try {
      await attendanceService.markExitManually(workshopId, participantName, user.name)
      loadAttendanceData() // إعادة تحميل البيانات
      setRefreshTrigger(prev => prev + 1) // تحديث الإحصائيات
      toast.success(`تم تسجيل خروج ${participantName}`)
    } catch (error) {
      console.error('Error marking exit:', error)
      toast.error('فشل في تسجيل الخروج')
    }
  }

  const markMultipleAttendance = (status) => {
    if (selectedParticipants.length === 0) {
      toast.error('يرجى اختيار المتدربين أولاً')
      return
    }

    const updatedRecords = [...attendanceRecords]
    
    selectedParticipants.forEach(participantName => {
      const existingIndex = updatedRecords.findIndex(record => record.participantName === participantName)
      const newRecord = {
        participantName,
        status,
        timestamp: new Date().toISOString(),
        method: 'manual_bulk',
        markedBy: user.name
      }

      if (existingIndex !== -1) {
        updatedRecords[existingIndex] = newRecord
      } else {
        updatedRecords.push(newRecord)
      }
    })

    saveAttendanceData(updatedRecords)
    setSelectedParticipants([])
    toast.success(`تم تسجيل ${status === 'present' ? 'حضور' : 'غياب'} ${selectedParticipants.length} متدرب`)
  }

  const calculateStats = () => {
    const stats = attendanceService.getAttendanceStats(workshopId, participants)
    setAttendanceStats(stats)
  }

  const exportAttendance = async () => {
    try {
      await attendanceService.exportAttendanceToExcel(workshopId, workshopTitle, participants)
      toast.success('تم تصدير سجل الحضور بنجاح!')
    } catch (error) {
      console.error('Error exporting attendance:', error)
      toast.error('فشل في تصدير سجل الحضور')
    }
  }

  const getParticipantStatus = (participantName) => {
    const record = attendanceRecords.find(r => r.participantName === participantName)
    return record ? record.status : 'not_marked'
  }

  const formatTimeRemaining = () => {
    if (!codeExpiry) return ''

    const now = new Date()
    const remaining = codeExpiry - now

    if (remaining <= 0) return 'منتهي الصلاحية'

    const minutes = Math.floor(remaining / (1000 * 60))
    const seconds = Math.floor((remaining % (1000 * 60)) / 1000)

    return `${minutes}:${seconds.toString().padStart(2, '0')}`
  }

  const formatExitTimeRemaining = () => {
    if (!exitCodeExpiry) return ''

    const now = new Date()
    const remaining = exitCodeExpiry - now

    if (remaining <= 0) return 'منتهي الصلاحية'

    const minutes = Math.floor(remaining / (1000 * 60))
    const seconds = Math.floor((remaining % (1000 * 60)) / 1000)

    return `${minutes}:${seconds.toString().padStart(2, '0')}`
  }

  return (
    <div className="space-y-6 max-w-7xl mx-auto">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            {t('workshops.attendanceManagement')}
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            {t('workshops.manageAttendance')}
          </p>
        </div>
        <div className="flex items-center space-x-2 rtl:space-x-reverse">
          <AttendanceNotifications
            workshopId={workshopId}
            onNotificationRead={(id) => console.log('تم قراءة الإشعار:', id)}
          />
          <Button
            variant="outline"
            onClick={exportAttendance}
            icon={Download}
            size="sm"
          >
            {t('workshops.exportAttendance')}
          </Button>
        </div>
      </div>

      {/* Statistics */}
      <AttendanceStats
        workshopId={workshopId}
        participants={participants}
        refreshTrigger={refreshTrigger}
      />

      {/* Code Sections Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Attendance Code Section */}
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
        <div className="flex items-center justify-between mb-4">
          <h4 className="text-lg font-medium text-gray-900 dark:text-white">
            {t('workshops.attendanceCode')}
          </h4>
          <div className="flex space-x-2 rtl:space-x-reverse">
            {!isCodeActive ? (
              <Button
                variant="primary"
                onClick={generateAttendanceCode}
                icon={QrCode}
                size="sm"
              >
                {t('workshops.generateCode')}
              </Button>
            ) : (
              <Button
                variant="outline"
                onClick={deactivateCode}
                icon={XCircle}
                size="sm"
              >
                {t('workshops.deactivateCode')}
              </Button>
            )}
          </div>
        </div>

        {isCodeActive && attendanceCode && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 p-6 rounded-lg border border-blue-200 dark:border-blue-700"
          >
            <div className="text-center">
              <div className="text-4xl font-bold text-blue-600 dark:text-blue-400 mb-2 font-mono">
                {attendanceCode}
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                يمكن للمتدربين استخدام هذا الكود لتسجيل الحضور
              </p>

              <div className="flex items-center justify-center space-x-4 rtl:space-x-reverse mb-4">
                <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                  <Clock className="h-4 w-4 mr-1" />
                  <span>{t('workshops.timeRemaining')}: {formatTimeRemaining()}</span>
                </div>
              </div>

              <Button
                variant="outline"
                onClick={copyCodeToClipboard}
                icon={Copy}
                size="sm"
              >
                {t('workshops.copyCode')}
              </Button>
            </div>
          </motion.div>
        )}

        {!isCodeActive && (
          <div className="text-center py-8 text-gray-500 dark:text-gray-400">
            <QrCode className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>{t('workshops.noActiveCode')}</p>
            <p className="text-sm">{t('workshops.clickToGenerate')}</p>
          </div>
        )}
        </div>

        {/* Exit Code Section */}
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
        <div className="flex items-center justify-between mb-4">
          <h4 className="text-lg font-medium text-gray-900 dark:text-white">
            {t('workshops.exitCode')}
          </h4>
          <div className="flex space-x-2 rtl:space-x-reverse">
            {!isExitCodeActive ? (
              <Button
                variant="secondary"
                onClick={generateExitCode}
                icon={QrCode}
                size="sm"
              >
                {t('workshops.generateExitCode')}
              </Button>
            ) : (
              <Button
                variant="outline"
                onClick={deactivateExitCode}
                icon={XCircle}
                size="sm"
              >
                {t('workshops.deactivateExitCode')}
              </Button>
            )}
          </div>
        </div>

        {isExitCodeActive && exitCode && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-gradient-to-r from-red-50 to-orange-50 dark:from-red-900/20 dark:to-orange-900/20 p-6 rounded-lg border border-red-200 dark:border-red-700"
          >
            <div className="text-center">
              <div className="text-4xl font-bold text-red-600 dark:text-red-400 mb-2 font-mono">
                {exitCode}
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                يمكن للمتدربين استخدام هذا الكود لتسجيل الخروج
              </p>

              <div className="flex items-center justify-center space-x-4 rtl:space-x-reverse mb-4">
                <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                  <Clock className="h-4 w-4 mr-1" />
                  <span>{t('workshops.timeRemaining')}: {formatExitTimeRemaining()}</span>
                </div>
              </div>

              <Button
                variant="outline"
                onClick={copyExitCodeToClipboard}
                icon={Copy}
                size="sm"
              >
                {t('workshops.copyExitCode')}
              </Button>
            </div>
          </motion.div>
        )}

        {!isExitCodeActive && (
          <div className="text-center py-8 text-gray-500 dark:text-gray-400">
            <QrCode className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>{t('workshops.noActiveExitCode')}</p>
            <p className="text-sm">{t('workshops.clickToGenerateExit')}</p>
          </div>
        )}
        </div>
      </div>

      {/* Manual Attendance Section */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
        <div className="flex items-center justify-between mb-4">
          <h4 className="text-lg font-medium text-gray-900 dark:text-white">
            {t('workshops.manualAttendance')}
          </h4>
          <Button
            variant="outline"
            onClick={() => setShowManualAttendance(!showManualAttendance)}
            icon={showManualAttendance ? Eye : Users}
            size="sm"
          >
            {showManualAttendance ? t('workshops.hideParticipants') : t('workshops.showParticipants')}
          </Button>
        </div>

        {showManualAttendance && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
          >
            {/* Bulk Actions */}
            {selectedParticipants.length > 0 && (
              <div className="mb-4 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-700">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-blue-600 dark:text-blue-400">
                    تم اختيار {selectedParticipants.length} متدرب
                  </span>
                  <div className="flex space-x-2 rtl:space-x-reverse">
                    <Button
                      variant="success"
                      onClick={() => markMultipleAttendance('present')}
                      icon={UserCheck}
                      size="sm"
                    >
                      تسجيل حضور الكل
                    </Button>
                    <Button
                      variant="danger"
                      onClick={() => markMultipleAttendance('absent')}
                      icon={UserX}
                      size="sm"
                    >
                      تسجيل غياب الكل
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => setSelectedParticipants([])}
                      size="sm"
                    >
                      إلغاء التحديد
                    </Button>
                  </div>
                </div>
              </div>
            )}

            {/* Participants List */}
            <div className="space-y-2">
              {participants.map((participant, index) => {
                const participantName = typeof participant === 'object' ? participant.name : participant
                const status = getParticipantStatus(participantName)
                const isSelected = selectedParticipants.includes(participantName)

                return (
                  <motion.div
                    key={participantName}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.05 }}
                    className={`flex items-center justify-between p-4 rounded-lg border ${
                      status === 'present'
                        ? 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-700'
                        : status === 'absent'
                        ? 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-700'
                        : 'bg-gray-50 dark:bg-gray-700 border-gray-200 dark:border-gray-600'
                    }`}
                  >
                    <div className="flex items-center space-x-3 rtl:space-x-reverse">
                      <input
                        type="checkbox"
                        checked={isSelected}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setSelectedParticipants([...selectedParticipants, participantName])
                          } else {
                            setSelectedParticipants(selectedParticipants.filter(name => name !== participantName))
                          }
                        }}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />

                      <div className="flex items-center space-x-2 rtl:space-x-reverse">
                        {typeof participant === 'object' && participant.profileImage ? (
                          <img
                            src={participant.profileImage}
                            alt={participantName}
                            className="w-8 h-8 rounded-full object-cover"
                          />
                        ) : (
                          <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-sm font-bold">
                            {participantName.charAt(0).toUpperCase()}
                          </div>
                        )}
                        <span className="font-medium text-gray-900 dark:text-white">
                          {participantName}
                        </span>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2 rtl:space-x-reverse">
                      {status === 'present' && (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400">
                          <CheckCircle className="h-3 w-3 mr-1" />
                          حاضر
                        </span>
                      )}
                      {status === 'absent' && (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400">
                          <XCircle className="h-3 w-3 mr-1" />
                          غائب
                        </span>
                      )}
                      {status === 'not_marked' && (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-400">
                          <Clock className="h-3 w-3 mr-1" />
                          لم يتم التسجيل
                        </span>
                      )}

                      <div className="flex space-x-1 rtl:space-x-reverse">
                        <Button
                          variant={status === 'present' ? 'success' : 'outline'}
                          onClick={() => markAttendanceManually(participantName, 'present')}
                          icon={UserCheck}
                          size="sm"
                        >
                          {t('workshops.present') || 'حاضر'}
                        </Button>
                        <Button
                          variant={status === 'absent' ? 'danger' : 'outline'}
                          onClick={() => markAttendanceManually(participantName, 'absent')}
                          icon={UserX}
                          size="sm"
                        >
                          {t('workshops.absent') || 'غائب'}
                        </Button>
                        <Button
                          variant="secondary"
                          onClick={() => markExitManually(participantName)}
                          icon={LogOut}
                          size="sm"
                        >
                          خروج
                        </Button>
                      </div>
                    </div>
                  </motion.div>
                )
              })}
            </div>

            {participants.length === 0 && (
              <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                <Users className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>لا يوجد متدربين مسجلين في هذه الورشة</p>
              </div>
            )}
          </motion.div>
        )}
      </div>

      {/* Attendance Reminders */}
      <AttendanceReminders
        workshopId={workshopId}
        workshopTitle={workshopTitle}
        participants={participants}
      />
    </div>
  )
}

export default AttendanceManager
